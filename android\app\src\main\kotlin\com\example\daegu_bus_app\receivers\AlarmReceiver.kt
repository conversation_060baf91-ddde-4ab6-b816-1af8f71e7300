package com.example.daegu_bus_app.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.example.daegu_bus_app.services.BusAlertService
import com.example.daegu_bus_app.services.TTSService
import com.example.daegu_bus_app.AlarmScheduler // AlarmScheduler 임포트 추가

class AlarmReceiver : BroadcastReceiver() {
    private val TAG = "AlarmReceiver"

    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        Log.d(TAG, "🔔 알람 수신: $action")

        if (action == "com.example.daegu_bus_app.AUTO_ALARM") {
            handleAutoAlarm(context, intent)
        }
    }

    private fun handleAutoAlarm(context: Context, intent: Intent) {
        try {
            val alarmId = intent.getIntExtra("alarmId", 0)
            val busNo = intent.getStringExtra("busNo") ?: return
            val stationName = intent.getStringExtra("stationName") ?: return
            val routeId = intent.getStringExtra("routeId") ?: return
            val stationId = intent.getStringExtra("stationId") ?: return
            val useTTS = intent.getBooleanExtra("useTTS", true)
            val isBackup = intent.getBooleanExtra("isBackup", false) // 백업 알람 여부

            // 현재 시간과 알람 정보 로깅
            val currentTime = System.currentTimeMillis()
            val calendar = java.util.Calendar.getInstance().apply { timeInMillis = currentTime }
            val timeString = String.format("%02d:%02d:%02d",
                calendar.get(java.util.Calendar.HOUR_OF_DAY),
                calendar.get(java.util.Calendar.MINUTE),
                calendar.get(java.util.Calendar.SECOND))

            Log.d(TAG, "🔔 자동 알람 처리 시작: $busNo 번 버스, $stationName")
            Log.d(TAG, "   ⏰ 현재 시간: $timeString")
            Log.d(TAG, "   🆔 알람 ID: $alarmId, 백업: $isBackup, TTS: $useTTS")
            Log.d(TAG, "   🚌 노선 ID: $routeId, 정류장 ID: $stationId")

            // TTS 서비스 시작
            if (useTTS) {
                Log.d(TAG, "🔊 TTS 서비스 시작 시도...")
                val ttsIntent = Intent(context, TTSService::class.java).apply {
                    action = "REPEAT_TTS_ALERT"
                    putExtra("busNo", busNo)
                    putExtra("stationName", stationName)
                    putExtra("routeId", routeId)
                    putExtra("stationId", stationId)
                    putExtra("isAutoAlarm", true)
                    putExtra("forceSpeaker", true)
                    putExtra("singleExecution", true) // 단일 실행 모드
                }

                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        context.startForegroundService(ttsIntent)
                    } else {
                        context.startService(ttsIntent)
                    }
                    Log.d(TAG, "✅ TTS 서비스 시작 성공")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ TTS 서비스 시작 실패: ${e.message}", e)
                }
            } else {
                Log.d(TAG, "🔇 TTS 비활성화됨 - TTS 서비스 시작 안함")
            }

            // 버스 알림 서비스 시작 (포그라운드)
            Log.d(TAG, "📱 버스 알림 서비스 시작 시도...")
            val busIntent = Intent(context, BusAlertService::class.java).apply {
                action = BusAlertService.ACTION_START_AUTO_ALARM_LIGHTWEIGHT
                putExtra("busNo", busNo)
                putExtra("stationName", stationName)
                putExtra("routeId", routeId)
                putExtra("stationId", stationId)
                putExtra("remainingMinutes", 0) // 기본값
                putExtra("currentStation", "")
                putExtra("isAutoAlarm", true)
            }

            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(busIntent)
                } else {
                    context.startService(busIntent)
                }
                Log.d(TAG, "✅ 버스 알림 서비스 시작 성공")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 버스 알림 서비스 시작 실패: ${e.message}", e)
            }

            // 다음 알람 스케줄링은 Flutter에서 담당하므로, 여기서는 별도로 스케줄링하지 않음
            // (AlarmScheduler를 통해 이미 다음 알람이 스케줄링되었을 것으로 가정)
            Log.d(TAG, "🔔 자동 알람 처리 완료: $busNo 번 버스")

        } catch (e: Exception) {
            Log.e(TAG, "❌ 자동 알람 처리 오류: ${e.message}", e)
        }
    }
} 