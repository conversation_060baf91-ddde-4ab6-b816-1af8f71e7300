package com.example.daegu_bus_app

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.example.daegu_bus_app.receivers.AlarmReceiver
import java.util.Calendar

object AlarmScheduler {
    private const val TAG = "AlarmScheduler"

    fun scheduleAlarm(
        context: Context,
        alarmId: Int,
        busNo: String,
        stationName: String,
        routeId: String,
        stationId: String,
        useTTS: Boolean,
        triggerAtMillis: Long, // Flutter에서 계산된 정확한 시간 (밀리초)
        isBackup: Boolean = false // 백업 알람 여부
    ) {
        try {
            Log.d(TAG, "AlarmManager 스케줄링: $busNo 번 버스, $stationName, triggerAtMillis: $triggerAtMillis, isBackup: $isBackup")

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

            val alarmIntent = Intent(context, AlarmReceiver::class.java).apply {
                action = "com.example.daegu_bus_app.AUTO_ALARM"
                putExtra("alarmId", alarmId)
                putExtra("busNo", busNo)
                putExtra("stationName", stationName)
                putExtra("routeId", routeId)
                putExtra("stationId", stationId)
                putExtra("useTTS", useTTS)
                putExtra("isBackup", isBackup) // 백업 알람 여부 전달
            }

            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.getBroadcast(
                    context,
                    alarmId,
                    alarmIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                PendingIntent.getBroadcast(
                    context,
                    alarmId,
                    alarmIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT
                )
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 정확한 알람을 위해 setAlarmClock 사용 (Doze 모드에서도 작동)
                alarmManager.setAlarmClock(
                    AlarmManager.AlarmClockInfo(triggerAtMillis, pendingIntent),
                    pendingIntent
                )
            } else {
                // 구 버전 안드로이드에서는 setExact 사용
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    triggerAtMillis,
                    pendingIntent
                )
            }

            Log.d(TAG, "✅ AlarmManager 스케줄링 완료: ${busNo}번 버스, ${Calendar.getInstance().apply { timeInMillis = triggerAtMillis }.time}")

        } catch (e: Exception) {
            Log.e(TAG, "AlarmManager 스케줄링 오류", e)
        }
    }

    fun cancelAlarm(context: Context, alarmId: Int) {
        try {
            Log.d(TAG, "AlarmManager 알람 취소: alarmId: $alarmId")
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val alarmIntent = Intent(context, AlarmReceiver::class.java).apply {
                action = "com.example.daegu_bus_app.AUTO_ALARM"
            }
            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.getBroadcast(
                    context,
                    alarmId,
                    alarmIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                PendingIntent.getBroadcast(
                    context,
                    alarmId,
                    alarmIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT
                )
            }
            alarmManager.cancel(pendingIntent)
            Log.d(TAG, "✅ AlarmManager 알람 취소 완료: alarmId: $alarmId")
        } catch (e: Exception) {
            Log.e(TAG, "AlarmManager 알람 취소 오류", e)
        }
    }
}
