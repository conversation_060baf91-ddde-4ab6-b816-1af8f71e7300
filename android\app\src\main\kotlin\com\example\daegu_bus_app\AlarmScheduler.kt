package com.example.daegu_bus_app

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.example.daegu_bus_app.receivers.AlarmReceiver
import java.util.Calendar

object AlarmScheduler {
    private const val TAG = "AlarmScheduler"

    fun scheduleAlarm(
        context: Context,
        alarmId: Int,
        busNo: String,
        stationName: String,
        routeId: String,
        stationId: String,
        useTTS: Boolean,
        triggerAtMillis: Long, // Flutter에서 계산된 정확한 시간 (밀리초)
        isBackup: Boolean = false // 백업 알람 여부
    ) {
        try {
            // 현재 시간과 알람 시간 정보 로깅
            val currentTime = System.currentTimeMillis()
            val delayMillis = triggerAtMillis - currentTime
            val delayMinutes = delayMillis / (1000 * 60)
            val delaySeconds = (delayMillis % (1000 * 60)) / 1000

            val triggerCalendar = java.util.Calendar.getInstance().apply { timeInMillis = triggerAtMillis }
            val triggerTimeString = String.format("%02d:%02d:%02d",
                triggerCalendar.get(java.util.Calendar.HOUR_OF_DAY),
                triggerCalendar.get(java.util.Calendar.MINUTE),
                triggerCalendar.get(java.util.Calendar.SECOND))

            Log.d(TAG, "🔔 AlarmManager 스케줄링 시작:")
            Log.d(TAG, "   🚌 버스: $busNo 번, 정류장: $stationName")
            Log.d(TAG, "   ⏰ 트리거 시간: $triggerTimeString")
            Log.d(TAG, "   ⏱️ 지연 시간: ${delayMinutes}분 ${delaySeconds}초")
            Log.d(TAG, "   🆔 알람 ID: $alarmId, 백업: $isBackup, TTS: $useTTS")

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

            val alarmIntent = Intent(context, AlarmReceiver::class.java).apply {
                action = "com.example.daegu_bus_app.AUTO_ALARM"
                putExtra("alarmId", alarmId)
                putExtra("busNo", busNo)
                putExtra("stationName", stationName)
                putExtra("routeId", routeId)
                putExtra("stationId", stationId)
                putExtra("useTTS", useTTS)
                putExtra("isBackup", isBackup) // 백업 알람 여부 전달
            }

            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.getBroadcast(
                    context,
                    alarmId,
                    alarmIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                PendingIntent.getBroadcast(
                    context,
                    alarmId,
                    alarmIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT
                )
            }

            // 알람 스케줄링 방법 선택 및 실행
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 정확한 알람을 위해 setAlarmClock 사용 (Doze 모드에서도 작동)
                Log.d(TAG, "   📱 Android M+ 감지 - setAlarmClock 사용")
                alarmManager.setAlarmClock(
                    AlarmManager.AlarmClockInfo(triggerAtMillis, pendingIntent),
                    pendingIntent
                )
            } else {
                // 구 버전 안드로이드에서는 setExact 사용
                Log.d(TAG, "   📱 Android M 미만 - setExact 사용")
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    triggerAtMillis,
                    pendingIntent
                )
            }

            Log.d(TAG, "✅ AlarmManager 스케줄링 완료: ${busNo}번 버스")
            Log.d(TAG, "   🎯 정확한 트리거 시간: $triggerTimeString")

        } catch (e: Exception) {
            Log.e(TAG, "❌ AlarmManager 스케줄링 오류: ${e.message}", e)
        }
    }

    fun cancelAlarm(context: Context, alarmId: Int) {
        try {
            Log.d(TAG, "AlarmManager 알람 취소: alarmId: $alarmId")
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val alarmIntent = Intent(context, AlarmReceiver::class.java).apply {
                action = "com.example.daegu_bus_app.AUTO_ALARM"
            }
            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.getBroadcast(
                    context,
                    alarmId,
                    alarmIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                PendingIntent.getBroadcast(
                    context,
                    alarmId,
                    alarmIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT
                )
            }
            alarmManager.cancel(pendingIntent)
            Log.d(TAG, "✅ AlarmManager 알람 취소 완료: alarmId: $alarmId")
        } catch (e: Exception) {
            Log.e(TAG, "AlarmManager 알람 취소 오류", e)
        }
    }
}
